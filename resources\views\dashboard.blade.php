<x-app-layout>
    <!-- Hero Section -->
    <div class="hero">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="hero-content text-center">
                <h1 class="animate-fade-in">Great designs come alive with motion!</h1>
                <p class="animate-slide-up">Create, edit, collaborate on and implement lightweight Lottie animations across websites, apps, socials and more.</p>
                <div class="mt-8">
                    <a href="#" class="btn-gradient px-8 py-3 rounded-full text-lg font-semibold inline-block">Get Started</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Features Section -->
    <div class="features bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl font-bold">The world's largest free, ready-to-use, customizable animation library</h2>
                <p class="mt-4 text-lg text-gray-600">Discover thousands of free Lottie animations for your next project</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="card feature-card">
                    <div class="feature-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h.01" />
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold mb-2">Lottie Creator</h3>
                    <p class="text-gray-600">A powerful web-based Lottie animation tool, designed to create ultra-lightweight animations.</p>
                </div>

                <div class="card feature-card">
                    <div class="feature-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold mb-2">Web Player</h3>
                    <p class="text-gray-600">Easily embed and control Lottie animations on your website with our powerful web player.</p>
                </div>

                <div class="card feature-card">
                    <div class="feature-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold mb-2">Mobile App</h3>
                    <p class="text-gray-600">Access your Lottie animations on the go with our mobile app for iOS and Android.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Stats Section -->
    <div class="py-16 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8 text-center">
                <div>
                    <div class="text-4xl font-bold text-primary">10x</div>
                    <p class="mt-2 text-gray-600">faster to ship</p>
                </div>
                <div>
                    <div class="text-4xl font-bold text-primary">5x</div>
                    <p class="mt-2 text-gray-600">faster page load speed</p>
                </div>
                <div>
                    <div class="text-4xl font-bold text-primary">45%</div>
                    <p class="mt-2 text-gray-600">increase in user engagement</p>
                </div>
                <div>
                    <div class="text-4xl font-bold text-primary">6x</div>
                    <p class="mt-2 text-gray-600">smaller than GIF</p>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
