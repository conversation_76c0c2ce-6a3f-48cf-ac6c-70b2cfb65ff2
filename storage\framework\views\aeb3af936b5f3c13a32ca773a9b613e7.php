<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <!-- Hero Section -->
    <div class="hero">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="hero-content text-center">
                <h1 class="animate-fade-in">Preview Lottie Animation Instantly</h1>
                <p class="animate-slide-up">Drag & drop your Lottie JSON files to preview them instantly with our Lottie player.</p>
            </div>
        </div>
    </div>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <?php if(session('error')): ?>
                        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                            <?php echo e(session('error')); ?>

                        </div>
                    <?php endif; ?>

                    <!-- Upload Area -->
                    <div id="drop-area" class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                        <div class="space-y-4">
                            <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                            </svg>
                            <div class="text-gray-600">
                                <p class="text-lg font-medium">Drag & drop your Lottie JSON file here</p>
                                <p class="text-sm">or</p>
                                <button id="browse-button" class="mt-2 btn-primary px-4 py-2 rounded-md text-sm">Browse Files</button>
                            </div>
                            <input type="file" id="lottie_file" class="hidden" accept=".json">
                        </div>
                    </div>

                    <!-- Preview Container -->
                    <div id="preview-container" class="hidden">
                        <div class="bg-white rounded-md shadow-md p-6">
                            <div class="flex justify-between items-center mb-6">
                                <h3 class="text-lg font-semibold" id="file-name-display">Preview</h3>
                                <div class="flex items-center space-x-4">
                                    
                                    <button id="close-preview" class="text-gray-500 hover:text-gray-700">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20"
                                            fill="currentColor">
                                            <path fill-rule="evenodd"
                                                d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                                                clip-rule="evenodd" />
                                        </svg>
                                    </button>
                                </div>
                            </div>

                            <div class="flex flex-col md:flex-row gap-8">
                                <!-- Lottie Preview -->
                                <div class="w-full md:w-2/3">
                                    <div id="lottie-container" class="bg-gray-100 rounded-lg p-4 flex items-center justify-center" style="min-height: 400px;">
                                        <lottie-player id="lottiePlayer" background="transparent" speed="1" style="width: 100%; height: 100%;" loop autoplay></lottie-player>
                                    </div>

                                    <!-- Playback Controls -->
                                    <div class="mt-4 flex flex-wrap items-center gap-2">
                                        <button id="play" class="btn-primary px-3 py-1 rounded-md text-sm">Play</button>
                                        <button id="pause" class="bg-gray-200 text-gray-700 px-3 py-1 rounded-md text-sm">Pause</button>
                                        <button id="stop" class="bg-gray-200 text-gray-700 px-3 py-1 rounded-md text-sm">Stop</button>
                                        <div class="flex items-center gap-2 ml-4">
                                            <button class="speed-btn bg-gray-700 text-white px-2 py-1 rounded text-sm" data-speed="0.25">0.25x</button>
                                            <button class="speed-btn bg-gray-200 text-gray-700 px-2 py-1 rounded text-sm" data-speed="0.5">0.5x</button>
                                            <button class="speed-btn bg-gray-200 text-gray-700 px-2 py-1 rounded text-sm" data-speed="1">1x</button>
                                            <button class="speed-btn bg-gray-200 text-gray-700 px-2 py-1 rounded text-sm" data-speed="1.5">1.5x</button>
                                            <button class="speed-btn bg-gray-200 text-gray-700 px-2 py-1 rounded text-sm" data-speed="2">2x</button>
                                        </div>
                                    </div>
                                </div>

                                <!-- Controls -->
                                <div class="w-full md:w-1/3">
                                    <div class="bg-gray-50 rounded-lg p-6">
                                        <h3 class="text-lg font-semibold mb-4">Animation Controls</h3>

                                        <div class="space-y-4">
                                            <div>
                                                <label for="background" class="block text-sm font-medium text-gray-700 mb-1">Background Color</label>
                                                <input type="color" id="background" value="#f3f4f6" class="w-full h-10 p-1 rounded border border-gray-300">
                                            </div>
                                            <!-- Lottie Color Editor -->
                                            <div id="color-editor" class="mt-4">
                                                <label class="block text-sm font-medium text-gray-700 mb-1">Edit Lottie Colors</label>
                                                <div id="color-swatches" class="grid grid-cols-2 gap-2 pr-10"></div>
                                            </div>
                                            <div class="flex items-center">
                                                <input type="checkbox" id="loop" checked class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                                                <label for="loop" class="ml-2 block text-sm text-gray-700">Loop Animation</label>
                                            </div>
                                            <div class="flex items-center">
                                                <input type="checkbox" id="autoplay" checked class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                                                <label for="autoplay" class="ml-2 block text-sm text-gray-700">Autoplay</label>
                                            </div>
                                            <div class="pt-4">
                                                <button id="download-json" class="w-full btn-primary px-4 py-2 rounded-md text-sm">Download JSON</button>
                                            </div>
                                            <?php if(auth()->guard()->check()): ?>
                                                <div class="pt-4" id="server-upload-container">
                                                    <form id="server-upload-form" class="w-full">
                                                        <button type="submit" class="w-full bg-gray-200 text-gray-700 px-4 py-2 rounded-md text-sm hover:bg-gray-300">Save to Server</button>
                                                    </form>
                                                </div>
                                            <?php else: ?>
                                                <div class="pt-4">
                                                    <a href="<?php echo e(route('login')); ?>" class="w-full bg-gray-200 text-gray-700 px-4 py-2 rounded-md text-sm hover:bg-gray-300 block text-center">Login to Save</a>
                                                </div>
                                            <?php endif; ?>
                                            <div class="pt-4 hidden" id="share-email-container">
                                                <label for="share-email"
                                                    class="block text-sm font-medium text-gray-700 mb-1">Share via Email</label>
                                                <div class="flex gap-2">
                                                    <input type="email" id="share-email" placeholder="Enter email address" class="flex-1 rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50">
                                                    <button id="share-button" class="btn-primary text-white px-4 py-2 rounded-md text-sm hover:bg-primary-dark"> Share </button>
                                                </div>
                                            </div>
                                            <div class="pt-4 hidden" id="share-link-container">
                                                <label for="share-link"
                                                    class="block text-sm font-medium text-gray-700 mb-1">Share via Link</label>
                                                <div class="flex gap-2">
                                                    <input type="text" id="share-link" readonly class="flex-1 rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50">
                                                    <button id="copy-link" class="btn-primary text-white px-4 py-2 rounded-md text-sm hover:bg-primary-dark"> Copy </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Features Section -->
    <div class="features bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl font-bold">Why Use Lottie Animations?</h2>
                <p class="mt-4 text-lg text-gray-600">Lottie animations offer numerous advantages over traditional
                    animation formats</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="card feature-card">
                    <div class="feature-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M13 10V3L4 14h7v7l9-11h-7z" />
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold mb-2">Lightweight</h3>
                    <p class="text-gray-600">Lottie files are significantly smaller than GIFs or videos, resulting in
                        faster loading times.</p>
                </div>

                <div class="card feature-card">
                    <div class="feature-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4" />
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold mb-2">Scalable</h3>
                    <p class="text-gray-600">Lottie animations are vector-based, so they scale perfectly to any size
                        without losing quality.</p>
                </div>

                <div class="card feature-card">
                    <div class="feature-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold mb-2">Interactive</h3>
                    <p class="text-gray-600">Lottie animations can be interactive, allowing users to control playback
                        and interact with the animation.</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const dropArea = document.getElementById('drop-area');
            const browseButton = document.getElementById('browse-button');
            const fileInput = document.getElementById('lottie_file');
            const previewContainer = document.getElementById('preview-container');
            const closePreview = document.getElementById('close-preview');
            const fileNameDisplay = document.getElementById('file-name-display');
            const lottiePlayer = document.getElementById('lottiePlayer');
            const lottieContainer = document.getElementById('lottie-container');
            const playBtn = document.getElementById('play');
            const pauseBtn = document.getElementById('pause');
            const stopBtn = document.getElementById('stop');
            const speedBtns = document.querySelectorAll('.speed-btn');
            const bgColorControl = document.getElementById('background');
            const loopControl = document.getElementById('loop');
            const autoplayControl = document.getElementById('autoplay');
            const downloadJsonBtn = document.getElementById('download-json');
            const serverUploadForm = document.getElementById('server-upload-form');

            let currentFile = null;
            let lottieJsonData = null; // Store parsed JSON for color editing
            let colorMap = {}; // { colorString: [ { path, value } ] }

            // Prevent default drag behaviors
            ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                dropArea.addEventListener(eventName, preventDefaults, false);
                document.body.addEventListener(eventName, preventDefaults, false);
            });

            // Highlight drop area when item is dragged over it
            ['dragenter', 'dragover'].forEach(eventName => {
                dropArea.addEventListener(eventName, highlight, false);
            });

            ['dragleave', 'drop'].forEach(eventName => {
                dropArea.addEventListener(eventName, unhighlight, false);
            });

            // Handle dropped files
            dropArea.addEventListener('drop', handleDrop, false);

            // Handle browse button
            browseButton.addEventListener('click', () => {
                fileInput.click();
            });

            // Handle file selection
            fileInput.addEventListener('change', () => {
                if (fileInput.files && fileInput.files[0]) {
                    handleFiles(fileInput.files);
                }
            });

            // Close preview
            closePreview.addEventListener('click', () => {
                previewContainer.classList.add('hidden');
                dropArea.classList.remove('hidden');
                lottiePlayer.load('');
                currentFile = null;
            });

            // Player controls
            playBtn.addEventListener('click', () => {
                lottiePlayer.play();
            });

            pauseBtn.addEventListener('click', () => {
                lottiePlayer.pause();
            });

            stopBtn.addEventListener('click', () => {
                lottiePlayer.stop();
            });

            // Speed controls
            speedBtns.forEach(btn => {
                btn.addEventListener('click', () => {
                    const speed = parseFloat(btn.getAttribute('data-speed'));
                    lottiePlayer.speed = speed;

                    // Update active button
                    speedBtns.forEach(b => {
                        b.classList.remove('bg-gray-700', 'text-white');
                        b.classList.add('bg-gray-200', 'text-gray-700');
                    });
                    btn.classList.remove('bg-gray-200', 'text-gray-700');
                    btn.classList.add('bg-gray-700', 'text-white');
                });
            });

            // Background color control
            bgColorControl.addEventListener('input', () => {
                lottieContainer.style.backgroundColor = bgColorControl.value;
            });

            // Loop control
            loopControl.addEventListener('change', () => {
                if (loopControl.checked) {
                    lottiePlayer.setAttribute('loop', '');
                } else {
                    lottiePlayer.removeAttribute('loop');
                }
            });

            // Autoplay control
            autoplayControl.addEventListener('change', () => {
                if (autoplayControl.checked) {
                    lottiePlayer.setAttribute('autoplay', '');
                    lottiePlayer.play();
                } else {
                    lottiePlayer.removeAttribute('autoplay');
                    lottiePlayer.pause();
                }
            });

            // Download JSON
            downloadJsonBtn.addEventListener('click', () => {
                if (lottieJsonData) {
                    const a = document.createElement('a');
                    a.href = URL.createObjectURL(new Blob([JSON.stringify(lottieJsonData, null, 2)], {
                        type: 'application/json'
                    }));
                    a.download = currentFile ? currentFile.name : 'animation.json';
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                } else if (currentFile) {
                    const a = document.createElement('a');
                    a.href = URL.createObjectURL(currentFile);
                    a.download = currentFile.name;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                }
            });
            let uuid = null;
            // Server upload form
            if (serverUploadForm) {
                serverUploadForm.addEventListener('submit', (e) => {
                    e.preventDefault();
                    if (currentFile) {
                        const formData = new FormData();
                        formData.append('lottie_file', currentFile);
                        formData.append('_token', '<?php echo e(csrf_token()); ?>');

                        fetch('<?php echo e(route('lottie.upload')); ?>', {
                                method: 'POST',
                                body: formData
                            })
                            .then(response => response.json())
                            .then(data => {
                                if (data.success) {
                                    // Store the share URL
                                    currentShareUrl = data.share_url;
                                    uuid = data.uuid;
                                    console.log("Share Url is : " + currentShareUrl);
                                    const saveToServerContainer = document.getElementById(
                                        'server-upload-container');
                                    saveToServerContainer.classList.add('hidden');
                                    const shareEmailContainer = document.getElementById(
                                        'share-email-container');
                                    shareEmailContainer.classList.remove('hidden');


                                    // alert(data.message);
                                } else {
                                    alert(data.message || 'Error uploading file');
                                }
                            })
                            .catch(error => {
                                console.error('Error:', error);
                                alert('Error uploading file');
                            });
                    }
                });
            }
            const shareButton = document.getElementById('share-button');
            const shareEmailInput = document.getElementById('share-email');
            let currentShareUrl = null; // Store the share URL

            // Update share button click handler
            shareButton.onclick = () => {
                const email = shareEmailInput.value.trim();
                if (!email) {
                    alert('Please enter an email address');
                    return;
                }
                if (!currentShareUrl) {
                    alert('No animation to share. Please upload and save an animation first.');
                    return;
                }

                // Extract UUID from the share URL
                // const urlParts = currentShareUrl.split('/');
                // const uuid = urlParts[urlParts.length - 1];
                console.log("UUID is : " + uuid);
                
                // Disable button and show loading state
                shareButton.disabled = true;
                shareButton.textContent = 'Sending...';

                // Send email request
                fetch(`/lottie/${uuid}/share-email`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
                    },
                    body: JSON.stringify({
                        email: email,
                        uuid: uuid,
                        share_url: currentShareUrl,
                        message: 'Check out this awesome Lottie animation!'
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        console.log('Animation shared successfully via email!', data);
                        // hide the share email container
                        const shareEmailContainer = document.getElementById('share-email-container');
                        shareEmailContainer.classList.add('hidden');
                        const shareLinkContainer = document.getElementById('share-link-container');
                        shareLinkContainer.classList.remove('hidden');
                        // Create a copy link button
                        const copyLinkBtn = document.getElementById('copy-link');
                        const shareLinkInput = document.getElementById('share-link');
                        shareLinkInput.value = data.url;
                        copyLinkBtn.addEventListener('click', () => {
                            navigator.clipboard.writeText(data.url).then(() => {
                                console.log('Link copied to clipboard!' , data.url);
                            });
                        });
                    } else {
                        alert(data.message || 'Error sharing animation');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error sharing animation');
                })
                .finally(() => {
                    // Re-enable button and restore text
                    shareButton.disabled = false;
                    shareButton.textContent = 'Share';
                });
            };
            function preventDefaults(e) {
                e.preventDefault();
                e.stopPropagation();
            }

            function highlight() {
                dropArea.classList.add('border-primary', 'bg-blue-50');
            }

            function unhighlight() {
                dropArea.classList.remove('border-primary', 'bg-blue-50');
            }

            function handleDrop(e) {
                const dt = e.dataTransfer;
                const files = dt.files;
                handleFiles(files);
            }

            function handleFiles(files) {
                if (files.length > 0) {
                    const file = files[0];
                    if (file.type === 'application/json' || file.name.endsWith('.json')) {
                        currentFile = file;
                        fileNameDisplay.textContent = file.name;
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            try {
                                lottieJsonData = JSON.parse(e.target.result);
                                colorMap = {};
                                extractColors(lottieJsonData);
                                renderColorEditor();
                                reloadLottieFromJson();
                                dropArea.classList.add('hidden');
                                previewContainer.classList.remove('hidden');
                            } catch (error) {
                                alert('Invalid JSON file. Please upload a valid Lottie JSON file.');
                                console.error('Error parsing JSON:', error);
                            }
                        };
                        reader.readAsText(file);
                    } else {
                        alert('Please upload a JSON file.');
                    }
                }
            }

            function extractColors(obj, path = []) {
                // Recursively find all solid color arrays in the Lottie JSON
                if (Array.isArray(obj)) {
                    obj.forEach((item, i) => extractColors(item, path.concat(i)));
                } else if (typeof obj === 'object' && obj !== null) {
                    for (const key in obj) {
                        if (key === 'c' && obj[key] && Array.isArray(obj[key].k)) {
                            // Solid color: { c: { k: [r,g,b,a] } }
                            const colorArr = obj[key].k;
                            if (Array.isArray(colorArr) && colorArr.length >= 3) {
                                const rgb = colorArr.slice(0, 3).map(x => Math.round(x * 255));
                                const hex = '#' + rgb.map(x => x.toString(16).padStart(2, '0')).join('');
                                if (!colorMap[hex]) colorMap[hex] = [];
                                colorMap[hex].push({
                                    path: path.concat([key, 'k']),
                                    value: colorArr
                                });
                            }
                        } else {
                            extractColors(obj[key], path.concat(key));
                        }
                    }
                }
            }

            function getValueByPath(obj, path) {
                return path.reduce((acc, key) => acc && acc[key], obj);
            }

            function setValueByPath(obj, path, value) {
                let ref = obj;
                for (let i = 0; i < path.length - 1; i++) ref = ref[path[i]];
                ref[path[path.length - 1]] = value;
            }

            function renderColorEditor() {
                const swatches = document.getElementById('color-swatches');
                swatches.innerHTML = '';
                const hexColors = Object.keys(colorMap);

                // Process colors in pairs
                for (let i = 0; i < hexColors.length; i += 2) {
                    const div = document.createElement('div');
                    div.className = 'flex items-center gap-2';

                    // First color
                    const hex1 = hexColors[i];
                    if (hex1 === '#NaNNaNNaN') continue;
                    const colorDiv1 = document.createElement('div');
                    colorDiv1.className = 'flex items-center gap-2';
                    colorDiv1.style.minWidth = '7rem';
                    colorDiv1.style.maxWidth = '7rem';
                    const input1 = createColorInput(hex1);
                    const label1 = document.createElement('span');
                    label1.textContent = hex1;
                    colorDiv1.appendChild(input1);
                    colorDiv1.appendChild(label1);
                    div.appendChild(colorDiv1);

                    // Second color (if exists)
                    if (i + 1 < hexColors.length) {
                        const hex2 = hexColors[i + 1];
                        if (hex2 === '#NaNNaNNaN') continue;
                        const colorDiv2 = document.createElement('div');
                        colorDiv2.className = 'flex items-center gap-2';
                        colorDiv2.style.marginLeft = '4rem';
                        const input2 = createColorInput(hex2);
                        const label2 = document.createElement('span');
                        label2.textContent = hex2;
                        colorDiv2.appendChild(input2);
                        colorDiv2.appendChild(label2);
                        div.appendChild(colorDiv2);
                    }

                    swatches.appendChild(div);
                }
            }

            function createColorInput(hex) {
                const input = document.createElement('input');
                input.type = 'color';
                input.value = hex;
                input.style.width = '32px';
                input.style.height = '32px';
                input.dataset.hex = hex;

                input.addEventListener('input', (e) => {
                    const newHex = e.target.value;
                    const r = parseInt(newHex.slice(1, 3), 16) / 255;
                    const g = parseInt(newHex.slice(3, 5), 16) / 255;
                    const b = parseInt(newHex.slice(5, 7), 16) / 255;

                    // Update the color in the JSON data
                    colorMap[hex].forEach(({
                        path,
                        value
                    }) => {
                        const a = value[3] !== undefined ? value[3] : 1;
                        setValueByPath(lottieJsonData, path, [r, g, b, a]);
                    });

                    // Use a more efficient update method
                    updateLottieWithNewColors();
                });

                return input;
            }

            function updateLottieWithNewColors() {
                // Get current state
                const currentTime = lottiePlayer.currentTime;
                const isPaused = lottiePlayer.isPaused;

                // Create a new animation source with updated colors
                const updatedJsonString = JSON.stringify(lottieJsonData);

                // Update the animation source without reloading the player
                lottiePlayer.load(URL.createObjectURL(new Blob([updatedJsonString], {
                    type: 'application/json'
                })));

                // Once loaded, restore state
                lottiePlayer.addEventListener('ready', function onReady() {
                    lottiePlayer.removeEventListener('ready', onReady);

                    // Restore time position
                    lottiePlayer.seek(currentTime);

                    // Restore play state
                    if (!isPaused) {
                        lottiePlayer.play();
                    }
                }, {
                    once: true
                });
            }

            function reloadLottieFromJson() {
                // Save playback state
                const isPaused = lottiePlayer.isPaused;
                const currentFrame = lottiePlayer.currentFrame;
                const isLooping = lottiePlayer.hasAttribute('loop');
                const isAutoplay = lottiePlayer.hasAttribute('autoplay');

                // Create a blob and reload the player
                const blob = new Blob([JSON.stringify(lottieJsonData)], {
                    type: 'application/json'
                });
                const objectUrl = URL.createObjectURL(blob);

                // Listen for the animation to be loaded
                function onReady() {
                    lottiePlayer.removeEventListener('ready', onReady);
                    // Restore the previous frame
                    lottiePlayer.seek(currentFrame);
                    if (!isPaused) {
                        lottiePlayer.play();
                    } else {
                        lottiePlayer.pause();
                    }
                    // Restore loop and autoplay attributes if needed
                    if (isLooping) lottiePlayer.setAttribute('loop', '');
                    else lottiePlayer.removeAttribute('loop');
                    if (isAutoplay) lottiePlayer.setAttribute('autoplay', '');
                    else lottiePlayer.removeAttribute('autoplay');
                }
                lottiePlayer.addEventListener('ready', onReady);
                lottiePlayer.load(objectUrl);
            }
        });
    </script>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH E:\Web_Development\showLottie\resources\views/lottie/index.blade.php ENDPATH**/ ?>