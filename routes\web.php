<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\LottieController;

Route::view('/', 'welcome');

Route::view('dashboard', 'dashboard')
    ->middleware(['auth', 'verified'])
    ->name('dashboard');

Route::view('profile', 'profile')
    ->middleware(['auth'])
    ->name('profile');

// Lottie Routes
Route::get('/lottie', [LottieController::class, 'index'])->name('lottie.index');
Route::post('/lottie/upload', [LottieController::class, 'upload'])->name('lottie.upload');
Route::get('/lottie/{uuid}', [LottieController::class, 'show'])->name('lottie.show');
Route::get('/lottie/preview/{uuid}', [LottieController::class, 'preview'])->name('lottie.preview');
Route::post('/lottie/{uuid}/share-email', [LottieController::class, 'shareViaEmail'])->name('lottie.share.email');

require __DIR__.'/auth.php';
