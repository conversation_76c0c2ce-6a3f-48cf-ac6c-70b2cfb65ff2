<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <title>{{ config('app.name', 'ShowLottie') }}</title>

        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.bunny.net">
        <link href="https://fonts.bunny.net/css?family=inter:400,500,600,700,800" rel="stylesheet" />

        <!-- Styles -->
        @vite(['resources/css/app.css', 'resources/css/lottie-theme.css', 'resources/js/app.js'])

        <!-- Lottie Player -->
        <script src="https://unpkg.com/@lottiefiles/lottie-player@latest/dist/lottie-player.js"></script>
    </head>
    <body class="antialiased font-sans">
        <div class="min-h-screen bg-gray-50">
            <header class="header">
                <div class="header-container max-w-7xl mx-auto">
                    <a href="/" class="logo flex items-center">
                        <span class="text-primary font-bold text-xl">ShowLottie</span>
                    </a>

                    <div class="hidden sm:flex items-center space-x-6">
                        <nav class="space-x-4">
                            <a href="{{ route('lottie.index') }}" class="text-gray-600 hover:text-primary">
                                Upload Lottie
                            </a>
                        </nav>

                        <div class="flex items-center space-x-3">
                            @if (Route::has('login'))
                                <div>
                                    @auth
                                        <a href="{{ url('/dashboard') }}" class="btn-primary px-4 py-2 rounded-md text-white">Dashboard</a>
                                    @else
                                        <a href="{{ route('login') }}" class="text-gray-600 hover:text-primary mr-4">Log in</a>

                                        @if (Route::has('register'))
                                            <a href="{{ route('register') }}" class="btn-primary px-4 py-2 rounded-md text-white">Register</a>
                                        @endif
                                    @endauth
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Mobile menu button -->
                    <div class="sm:hidden">
                        <button type="button" class="text-gray-600 hover:text-primary focus:outline-none">
                            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                            </svg>
                        </button>
                    </div>
                </div>
            </header>

            <div class="relative min-h-[calc(100vh-70px)] flex flex-col items-center justify-center">
                <!-- Hero Section -->
                <div class="hero-content text-center py-16">
                    <h1 class="text-4xl md:text-5xl font-bold mb-6 animate-fade-in">Great designs come alive with motion!</h1>
                    <p class="text-xl md:text-2xl mb-8 animate-slide-up">Create, edit, and preview lightweight Lottie animations for your websites and apps.</p>
                    <div class="flex flex-col sm:flex-row justify-center gap-4">
                        <a href="{{ route('lottie.index') }}" class="btn-gradient px-8 py-3 rounded-full text-lg font-semibold inline-block">Upload Lottie</a>
                        <a href="https://lottiefiles.com" target="_blank" class="bg-white text-primary border border-primary px-8 py-3 rounded-full text-lg font-semibold inline-block hover:bg-gray-50">Learn More</a>
                    </div>

                    <!-- Sample Lottie Animation -->
                    <div class="mt-12 flex justify-center">
                        <lottie-player
                            src="https://assets2.lottiefiles.com/packages/lf20_UJNc2t.json"
                            background="transparent"
                            speed="1"
                            style="width: 300px; height: 300px;"
                            loop
                            autoplay>
                        </lottie-player>
                    </div>
                </div>

                    <!-- Features Section -->
                    <div class="features bg-white py-16">
                        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                            <div class="text-center mb-16">
                                <h2 class="text-3xl font-bold">Why Use Lottie Animations?</h2>
                                <p class="mt-4 text-lg text-gray-600">Lottie animations offer numerous advantages over traditional animation formats</p>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                                <div class="card feature-card">
                                    <div class="feature-icon">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                                        </svg>
                                    </div>
                                    <h3 class="text-xl font-bold mb-2">Lightweight</h3>
                                    <p class="text-gray-600">Lottie files are significantly smaller than GIFs or videos, resulting in faster loading times.</p>
                                </div>

                                <div class="card feature-card">
                                    <div class="feature-icon">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4" />
                                        </svg>
                                    </div>
                                    <h3 class="text-xl font-bold mb-2">Scalable</h3>
                                    <p class="text-gray-600">Lottie animations are vector-based, so they scale perfectly to any size without losing quality.</p>
                                </div>

                                <div class="card feature-card">
                                    <div class="feature-icon">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                        </svg>
                                    </div>
                                    <h3 class="text-xl font-bold mb-2">Interactive</h3>
                                    <p class="text-gray-600">Lottie animations can be interactive, allowing users to control playback and interact with the animation.</p>
                                </div>
                            </div>

                            <div class="text-center mt-12">
                                <a href="{{ route('lottie.index') }}" class="btn-primary px-6 py-2 rounded-md inline-block">Get Started</a>
                            </div>
                        </div>
                    </div>

                    <!-- Footer -->
                    <footer class="footer">
                        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                                <div class="footer-links">
                                    <h4>ShowLottie</h4>
                                    <p class="mt-2 text-sm text-gray-300">
                                        Create, edit, collaborate on and implement lightweight Lottie animations across websites, apps, socials and more.
                                    </p>
                                </div>

                                <div class="footer-links">
                                    <h4>Products</h4>
                                    <ul>
                                        <li><a href="#">Free Animations</a></li>
                                        <li><a href="#">Marketplace</a></li>
                                        <li><a href="#">Lottie Editor</a></li>
                                        <li><a href="#">Lottie Creator</a></li>
                                    </ul>
                                </div>

                                <div class="footer-links">
                                    <h4>Resources</h4>
                                    <ul>
                                        <li><a href="#">Blog</a></li>
                                        <li><a href="#">What is Lottie</a></li>
                                        <li><a href="#">Community</a></li>
                                        <li><a href="#">Developer Portal</a></li>
                                    </ul>
                                </div>

                                <div class="footer-links">
                                    <h4>Company</h4>
                                    <ul>
                                        <li><a href="#">About Us</a></li>
                                        <li><a href="#">Careers</a></li>
                                        <li><a href="#">Contact</a></li>
                                        <li><a href="#">Privacy Policy</a></li>
                                    </ul>
                                </div>
                            </div>

                            <div class="footer-bottom">
                                <p>&copy; {{ date('Y') }} ShowLottie. All rights reserved.</p>
                            </div>
                        </div>
                    </footer>
                </div>
            </div>
        </div>
    </body>
</html>
