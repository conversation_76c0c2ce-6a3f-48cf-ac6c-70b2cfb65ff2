<?php
namespace App\Http\Controllers;

use App\Models\LottieFile;
use App\Mail\ShareLottieAnimation;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;

class LottieController extends Controller
{
    /**
     * Show the Lottie uploader and previewer page.
     */
    public function index()
    {
        return view('lottie.index');
    }


    /**
     * Extract metadata from the Lottie JSON file.
     */
    private function extractMetadata($file): array
    {
        $content = file_get_contents($file->getPathname());
        $json = json_decode($content, true);

        return [
            'width' => $json['w'] ?? null,
            'height' => $json['h'] ?? null,
            'frames' => $json['fr'] ?? null,
            'total_frames' => $json['op'] ?? null,
            'version' => $json['v'] ?? null,
            'assets' => count($json['assets'] ?? []),
            'layers' => count($json['layers'] ?? []),
        ];
    }

    /**
     * Handle the upload of a Lottie file.
     */
    public function upload(Request $request)
    {
        try {
            // Validate the request
            $request->validate([
                'lottie_file' => 'required|file|max:10240', // Max 10MB
            ]);

            $file = $request->file('lottie_file');

            // Validate JSON content
            $content = file_get_contents($file->getPathname());
            $json = json_decode($content, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid JSON file format'
                ], 422);
            }

            // Extract metadata
            $metadata = $this->extractMetadata($file);

            // Generate unique filename
            $fileName = Str::uuid() . '.json';
            $filePath = 'private/lottie/' . $fileName;

            // Store the file
            Storage::put($filePath, $content);

            // Create database record
            $lottieFile = LottieFile::create([
                'original_name' => $file->getClientOriginalName(),
                'file_name' => $fileName,
                'file_path' => $filePath,
                'mime_type' => $file->getMimeType(),
                'file_size' => $file->getSize(),
                'metadata' => $metadata,
                'is_valid' => true,
                'status' => 'active',
                'user_id' => auth()->id(),
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Lottie file uploaded successfully',
                'share_url' => route('lottie.show', ['uuid' => $lottieFile->uuid]),
                'uuid' => $lottieFile->uuid,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error uploading file: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display a shared Lottie animation.
     */
    public function show($uuid)
    {
        // dd("here ", $uuid);
        $lottieFile = LottieFile::where('uuid', $uuid)->firstOrFail();
        // dd("here we are" , $lottieFile->file_path);
        // Check if file exists in storage
        if (!Storage::exists($lottieFile->file_path)) {
            abort(404, 'Lottie file not found in storage');
        }
        // dd($lottieFile);
        // Get the file content to verify it's readable
        try {
            $content = Storage::get($lottieFile->file_path);
            $json = json_decode($content, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                abort(400, 'Invalid JSON file');
            }
        } catch (\Exception $e) {
            abort(500, 'Error reading Lottie file');
        }

        return view('lottie.show', compact('lottieFile'));
    }

    /**
     * Preview a Lottie file.
     */
    public function preview($uuid)
    {
        $lottieFile = LottieFile::where('uuid', $uuid)->firstOrFail();

        if (!Storage::exists($lottieFile->file_path)) {
            abort(404, 'Lottie file not found');
        }

        return response(Storage::get($lottieFile->file_path))
            ->header('Content-Type', 'application/json')
            ->header('Content-Disposition', 'inline');
    }

    public function shareViaEmail(Request $request, $uuid)
    {
        // Validate email and optional message
        $request->validate([
            'email' => 'required|email',
            'uuid' => 'required|string',
            'share_url' => 'required|url',
            'message' => 'nullable|string|max:500'
        ]);
        
        $lottieFile = LottieFile::where('uuid', $uuid)->firstOrFail();
        $shareUrl = $request->share_url;

        // Send email using Laravel's Mail facade
        Mail::to($request->email)->send(
            new ShareLottieAnimation($lottieFile, $shareUrl, $request->message)
        );
        // Return a success response if email is sent successfully
        return response()->json(['success' => true, 'message' => 'Email sent successfully', 'url' => $shareUrl], 200);
    }
}

